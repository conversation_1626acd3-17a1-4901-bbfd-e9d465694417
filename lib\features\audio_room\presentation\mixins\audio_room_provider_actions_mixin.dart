import 'package:agora_rtm/agora_rtm.dart';
import 'package:flutter_audio_room/core/utils/analytics_utils.dart';
import 'package:flutter_audio_room/features/audio_room/constants/room_constants.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_enums.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_position.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_user.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/audio_room_provider_metadata_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/audio_room_provider_mic_mixin.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/error_handler.dart';

mixin AudioRoomActionsMixin
    on AudioRoomProviderMicMixin, AudioRoomProviderMetadataMixin {
  /// Kick user out of room
  Future<VoidResult> kickUser(int targetUid, String targetUserId) async {
    return ErrorHandler.handle(
      action: () async {
        // check if user has permission to kick
        if (!state.isCreatorOrManager) {
          return Left(ErrorHandler.createPermissionError('No permission'));
        }

        // if target is creator, cannot kick
        if (targetUid == state.creator?.uid) {
          return Left(
              ErrorHandler.createValidationError('Cannot kick creator'));
        }

        // if target is manager, only creator can kick
        if (state.isManager && targetUid == state.manager?.uid) {
          return Left(ErrorHandler.createValidationError(
              'Manager cannot kick manager'));
        }

        // 1. if user is on mic, drop mic first
        if (state.isOnMic(targetUid)) {
          final dropResult = await dropMic(targetUid);
          if (dropResult.isLeft()) return dropResult;
        }

        // 2. if target is manager, remove manager first
        if (targetUid == state.manager?.uid) {
          final removeManagerResult = await removeManager(
            targetUid: targetUid,
            targetUserId: targetUserId,
          );
          if (removeManagerResult.isLeft()) return removeManagerResult;
        }

        // 3. update members
        final newMembers = Map<int, RoomUser>.from(state.members);
        newMembers.remove(targetUid);
        state = state.copyWith(
          members: newMembers,
        );

        // 4. remove user
        final message = defaultRoomMessage.createEventMessage(
          roomId: state.currentRoom?.id ?? '',
          senderId: state.currentUid,
          sender: state.currentUser,
          targetId: targetUid,
          event: RoomMessageEvent.actionMessage,
          eventSubtype: RoomMessageEventSubtype.kickOut,
        );
        final result = await repository.kickUser(
          roomId: state.currentRoom?.id ?? '',
          kickedUserId: targetUserId,
        );
        if (result.isLeft()) return result;

        return audioService.sendChannelMessage(message);
      },
      identifier: 'kick_user',
    );
  }

  /// Toggle mute
  Future<VoidResult> toggleMuteByLocal() async {
    if (state.isMutedByManager) {
      return Left(
          ErrorHandler.createPermissionError('You are muted by manager'));
    }

    return ErrorHandler.handle(
      action: () async {
        var currentUser = state.currentUser;
        if (currentUser == null) {
          return Left(ErrorHandler.createValidationError('Invalid user'));
        }

        currentUser = currentUser.copyWith(
          isMutedBySelf: !state.isMutedBySelf,
        );
        state = state.copyWith(
          currentUser: currentUser,
          members: {
            ...state.members,
            state.currentUid!: currentUser,
          },
        );

        final eventSubtype = state.isMutedBySelf
            ? RoomMessageEventSubtype.userMuteMic
            : RoomMessageEventSubtype.userUnmuteMic;

        final message = defaultRoomMessage.createEventMessage(
          roomId: state.currentRoom?.id ?? '',
          senderId: state.currentUid,
          sender: state.currentUser,
          targetId: state.currentUid,
          event: RoomMessageEvent.actionMessage,
          eventSubtype: eventSubtype,
        );

        final muteLocalAudioResult =
            await audioService.muteLocalAudio(state.isMutedBySelf);
        if (muteLocalAudioResult.isLeft()) return muteLocalAudioResult;

        final setUserMetadataResult = await setUserMetadata(currentUser);
        if (setUserMetadataResult.isLeft()) return setUserMetadataResult;

        // Track mic action
        await AnalyticsUtils.trackMicAction(
          roomId: state.currentRoom?.id ?? '',
          action: state.isMutedBySelf ? 'mute' : 'unmute',
          success: true,
        );

        return await audioService.sendChannelMessage(message);
      },
      identifier: 'toggle_mute',
    );
  }

  Future<VoidResult> toggleMuteByRemote(
    int uid, {
    required bool mute,
  }) async {
    return ErrorHandler.handle(
      action: () async {
        final targetUserIsManager = state.manager?.uid == uid;
        final targetUserIsCreator = state.creator?.uid == uid;
        final targetUserIsSelf = state.currentUid == uid;
        if (!state.isCreatorOrManager) {
          return Left(ErrorHandler.createPermissionError(
              'You are not creator or manager'));
        }

        if (targetUserIsSelf) {
          return toggleMuteByLocal();
        }

        if (targetUserIsManager && !state.isCreator) {
          return Left(
              ErrorHandler.createPermissionError('You cannot mute manager'));
        }

        if (targetUserIsCreator) {
          return Left(
              ErrorHandler.createPermissionError('You cannot mute creator'));
        }

        var member = state.members[uid];
        final isMutedByManager = member?.isMutedByManager ?? false;
        if (member == null) {
          return Left(ErrorHandler.createValidationError('Invalid user'));
        }

        member = member.copyWith(
          isMutedByManager: !isMutedByManager,
        );
        state = state.copyWith(
          members: {
            ...state.members,
            uid: member,
          },
        );

        final message = defaultRoomMessage.createEventMessage(
          roomId: state.currentRoom?.id ?? '',
          senderId: state.currentUid,
          sender: state.currentUser,
          targetId: uid,
          event: RoomMessageEvent.actionMessage,
          eventSubtype: mute
              ? RoomMessageEventSubtype.muteMic
              : RoomMessageEventSubtype.unmuteMic,
        );

        return await audioService.sendChannelMessage(message);
      },
      identifier: 'mute_remote_user',
    );
  }

  /// Change seat
  Future<VoidResult> changeSeat(
    int oldSeat,
    int targetSeat,
  ) async {
    return ErrorHandler.handle(
      action: () async {
        final message = defaultRoomMessage
            .createEventMessage(
              roomId: state.currentRoom?.id ?? '',
              senderId: state.currentUid,
              sender: state.currentUser,
              targetId: state.currentUid,
              event: RoomMessageEvent.actionMessage,
              eventSubtype: RoomMessageEventSubtype.changePositionRequest,
            )
            .copyWith(
              position: RoomPosition(
                targetPosition: targetSeat,
                oldPosition: oldSeat,
              ),
            );

        return await audioService.sendChannelMessage(message);
      },
      identifier: 'change_seat',
    );
  }

  /// Drop mic
  /// [uid] User ID
  /// [onLeave] Whether the user is leaving the room
  Future<VoidResult> dropMic(
    int uid, {
    bool onLeave = false,
  }) async {
    return ErrorHandler.handle(
      action: () async {
        VoidResult result;
        if (state.currentUid == uid) {
          result = await repository.quitMic(
            roomId: state.currentRoom?.id ?? '',
          );
        } else {
          result = await repository.removeMic(
            roomId: state.currentRoom?.id ?? '',
            targetUserId: state.members[uid]?.userId ?? '',
          );
        }
        if (result.isLeft()) return result;

        final seat = state.users.entries
            .firstWhere(
              (element) => element.value == uid,
              orElse: () => const MapEntry(-1, -1),
            )
            .key;

        // make sure seat between host and max seat
        if (seat <= RoomConstants.hostSeatPosition ||
            seat > RoomConstants.maxSeatPosition) {
          return Left(ErrorHandler.createValidationError('Invalid seat'));
        }

        var member = state.members[uid];
        if (member == null) {
          return Left(ErrorHandler.createValidationError('Invalid user'));
        }

        member = member.copyWith(
          position: null,
          isMutedBySelf: false,
          isMutedByManager: false,
        );

        late final VoidResult updateMetadataResult;
        if (onLeave) {
          updateMetadataResult = await audioService.removeChannelMetadata(
            [RtmMetadataKey.seats()[seat]],
          );
        } else {
          updateMetadataResult = await audioService.updateChannelMetadata(
            [
              MetadataItem(
                key: RtmMetadataKey.seats()[seat],
                value: 'null',
              ),
            ],
          );
        }
        if (updateMetadataResult.isLeft()) return updateMetadataResult;

        var newState = state.copyWith(
          members: {
            ...state.members,
            uid: member,
          },
        );

        state = newState;

        if (state.currentUid == uid) {
          newState = newState.copyWith(
            currentUser: member,
          );
        }

        final setUserMetadataResult = await setUserMetadata(member);
        if (setUserMetadataResult.isLeft()) return setUserMetadataResult;

        final message = defaultRoomMessage
            .createEventMessage(
              roomId: state.currentRoom?.id ?? '',
              senderId: uid,
              sender: member,
              targetId: uid,
              event: RoomMessageEvent.userStatus,
              eventSubtype: RoomMessageEventSubtype.dropOnMic,
            )
            .copyWith(
              content: "${member.firstName} has mic removed",
            );

        // Track mic action
        await AnalyticsUtils.trackMicAction(
          roomId: state.currentRoom?.id ?? '',
          action: 'drop_mic',
          seatPosition: seat,
          success: true,
        );

        return await audioService.sendChannelMessage(message);
      },
      identifier: 'drop_remote_mic',
    );
  }

  /// Request mic seat
  Future<VoidResult> requestMic(int targetSeat) async {
    if (targetSeat < RoomConstants.minSeatPosition ||
        targetSeat > RoomConstants.maxSeatPosition) {
      return Left(ErrorHandler.createValidationError('Invalid seat position'));
    }

    if (state.lastRequestSeatTime != null &&
        DateTime.now().millisecondsSinceEpoch - state.lastRequestSeatTime! <
            5000) {
      return Left(ErrorHandler.createValidationError(
        'Please wait 5 seconds before requesting again',
      ));
    }

    return ErrorHandler.handle(
      action: () async {
        final message = defaultRoomMessage
            .createEventMessage(
              roomId: state.currentRoom?.id ?? '',
              senderId: state.currentUid,
              sender: state.currentUser,
              targetId: state.currentUid,
              event: RoomMessageEvent.actionMessage,
              eventSubtype: RoomMessageEventSubtype.micRequest,
            )
            .copyWith(
              position: RoomPosition(
                targetPosition: targetSeat,
                oldPosition: null,
              ),
            );

        // 立即将消息添加到本地房间消息流中，让发送方能立即看到自己的消息
        addMessage(message);

        final result = await audioService.sendChannelMessage(message);
        if (result.isLeft()) return result;

        state = state.copyWith(
          lastRequestSeatTime: DateTime.now().millisecondsSinceEpoch,
        );
        return const Right(null);
      },
      identifier: 'request_mic_seat',
    );
  }

  Future<VoidResult> handleMicRequest({
    int? targetUid,
    int? targetSeat,
    bool? accept,
  }) async {
    if (targetUid == null ||
        targetSeat == null ||
        targetSeat < RoomConstants.hostSeatPosition ||
        targetSeat > RoomConstants.maxSeatPosition) {
      return Left(
          ErrorHandler.createValidationError('Invalid uid or target seat'));
    }

    if (accept != true) {
      return declineMicRequest(targetUid);
    }

    return acceptMicRequest(
      targetUid: targetUid,
      targetSeat: targetSeat,
    );
  }

  /// invite mic
  /// [targetUid] Target user ID
  Future<VoidResult> inviteMic(int targetUid) async {
    return ErrorHandler.handle(
      action: () async {
        final result = await repository.inviteMic(
          roomId: state.currentRoom?.id ?? '',
          targetUserId: state.members[targetUid]?.userId ?? '',
        );
        if (result.isLeft()) return result;

        final message = defaultRoomMessage.createEventMessage(
          roomId: state.currentRoom?.id ?? '',
          senderId: state.currentUid,
          sender: state.currentUser,
          targetId: targetUid,
          event: RoomMessageEvent.actionMessage,
          eventSubtype: RoomMessageEventSubtype.inviteOnMic,
        );

        // 立即将消息添加到本地房间消息流中
        addMessage(message);

        return audioService.sendChannelMessage(message);
      },
      identifier: 'invite_mic',
    );
  }

  /// Handle mic invite
  /// [targetUid] Target user ID
  /// [accept] Accept invite
  Future<VoidResult> handleMicInvite(
    int? targetUid,
    bool accept,
  ) async {
    if (targetUid == null) {
      return Left(ErrorHandler.createValidationError('Invalid uid'));
    }

    final message = defaultRoomMessage.createEventMessage(
      roomId: state.currentRoom?.id ?? '',
      senderId: state.currentUid,
      sender: state.currentUser,
      targetId: targetUid,
      event: RoomMessageEvent.actionMessage,
      eventSubtype: accept
          ? RoomMessageEventSubtype.agreeInviteOnMic
          : RoomMessageEventSubtype.rejectInviteOnMic,
    );

    return ErrorHandler.handle(
      action: () async {
        // 立即将消息添加到本地房间消息流中
        addMessage(message);

        if (accept) {
          return acceptMicInvite(targetUid, message);
        }

        return declineMicInvite(targetUid, message);
      },
      identifier: 'handle_mic_invite',
    );
  }

  Future<VoidResult> inviteManager(int targetUid) async {
    return ErrorHandler.handle(
      action: () async {
        final message = defaultRoomMessage.createEventMessage(
          roomId: state.currentRoom?.id ?? '',
          senderId: state.currentUid,
          sender: state.currentUser,
          targetId: targetUid,
          event: RoomMessageEvent.actionMessage,
          eventSubtype: RoomMessageEventSubtype.inviteManager,
        );

        // 立即将消息添加到本地房间消息流中
        addMessage(message);

        return audioService.sendChannelMessage(message);
      },
      identifier: 'invite_manager',
    );
  }

  Future<VoidResult> removeManager({
    required int targetUid,
    required String targetUserId,
  }) async {
    return ErrorHandler.handle(
      action: () async {
        VoidResult requestResult;
        if (state.currentUid == targetUid) {
          requestResult = await repository.quitManager(
            roomId: state.currentRoom?.id ?? '',
          );
        } else {
          requestResult = await repository.removeManager(
            roomId: state.currentRoom?.id ?? '',
            targetUserId: targetUserId,
          );
        }
        if (requestResult.isLeft()) return requestResult;

        final result = await audioService.setChannelMultiMetadata(
          [
            MetadataItem(
              key: RtmMetadataKey.managerId.name,
              value: 'null',
            ),
          ],
        );
        if (result.isLeft()) return result;

        final message = defaultRoomMessage.createEventMessage(
          roomId: state.currentRoom?.id ?? '',
          senderId: state.currentUid,
          sender: state.currentUser,
          targetId: targetUid,
          event: RoomMessageEvent.actionMessage,
          eventSubtype: RoomMessageEventSubtype.removeManager,
        );

        // 立即将消息添加到本地房间消息流中
        addMessage(message);

        return audioService.sendChannelMessage(message);
      },
      identifier: 'remove_manager',
    );
  }

  Future<VoidResult> handleManagerInvite(
    int? targetUid,
    bool accept,
  ) async {
    if (targetUid == null) {
      return Left(ErrorHandler.createValidationError('Invalid uid'));
    }

    final message = defaultRoomMessage.createEventMessage(
      roomId: state.currentRoom?.id ?? '',
      senderId: state.currentUid,
      sender: state.currentUser,
      targetId: targetUid,
      event: RoomMessageEvent.actionMessage,
      eventSubtype: accept
          ? RoomMessageEventSubtype.agreeInviteManager
          : RoomMessageEventSubtype.rejectInviteManager,
    );

    return ErrorHandler.handle(
      action: () async {
        // 立即将消息添加到本地房间消息流中
        addMessage(message);

        return audioService.sendChannelMessage(message);
      },
      identifier: 'handle_manager_invite',
    );
  }
  
  /// Update room title
  Future<VoidResult> updateRoomTitle(String title) async {
    return ErrorHandler.handle(
      action: () async {
        state = state.copyWith(
          currentRoom: state.currentRoom?.copyWith(title: title),
        );

        return await audioService.setChannelMultiMetadata(
          [
            MetadataItem(
              key: RtmMetadataKey.title.name,
              value: title,
            ),
          ],
        );
      },
      identifier: 'update_room_title',
    );
  }

  /// Update room announcement
  Future<VoidResult> updateRoomAnnouncement(String announcement) async {
    return ErrorHandler.handle(
      action: () async {
        state = state.copyWith(
          currentRoom: state.currentRoom?.copyWith(announcement: announcement),
        );

        return await audioService.setChannelMultiMetadata(
          [
            MetadataItem(
              key: RtmMetadataKey.announcement.name,
              value: announcement,
            ),
          ],
        );
      },
      identifier: 'update_room_announcement',
    );
  }
}
