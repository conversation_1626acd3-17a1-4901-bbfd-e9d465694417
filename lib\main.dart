import 'dart:async';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_audio_room/app.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/app_lifecycle.dart';
import 'package:flutter_audio_room/core/utils/local_notification_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/main/observers.dart';
import 'package:flutter_audio_room/services/token_refresh/token_refresh_service.dart';
import 'package:flutter_audio_room/shared/data/consts/sp_keys.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_timezone/flutter_timezone.dart';

ProviderContainer providerContainer = ProviderContainer(
  observers: [Observers()],
  overrides: [
    // Example: override API URL based on environment
    // apiUrlProvider.overrideWithValue(EnvInfo.instance.apiUrl),
  ],
);


FutureOr<void> main() async {
  mainCommon();
}

Future<void> mainCommon() async {
  try {
    initAppLifecycleObserver();
    await _initializeApp();
    await _refreshToken();
    await _initializeAuth(providerContainer);
    
    runApp(_buildApp());
  } catch (e, stackTrace) {
    debugPrint('Initialization failed: $e');
    debugPrint(stackTrace.toString());
    rethrow;
  }
}

Future<void> _initializeApp() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  await _initializeFirebase();

  await NotificationService.initialize();

  await initializeAppModule();

  try {
    final currentTimeZone = await FlutterTimezone.getLocalTimezone();
    await getIt<StorageService>().setString(SPKeys.timezone, currentTimeZone);
  } catch (e) {
    LogUtils.d('Failed to get timezone: $e', tag: 'main._initializeApp');
  }
}

Future<void> _initializeFirebase() async {
  await Firebase.initializeApp();

  FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };
}

Future<void> _initializeAuth(ProviderContainer container) async {
  await container.read(accountProvider.notifier).initializeUser();
}

Future<void> _refreshToken() async {
  try {
    final refreshed = await getIt<TokenRefreshService>().refreshToken();
    refreshed.fold(
      (error) => LogUtils.d('Failed to refresh token: ${error.message}',
          tag: 'main._refreshToken'),
      (success) =>
          LogUtils.d('Refreshed token: $success', tag: 'main._refreshToken'),
    );
  } catch (e) {
    LogUtils.d('Failed to fetch user: $e', tag: 'main._refreshToken');
  }
}

Widget _buildApp() {
  return UncontrolledProviderScope(
    container: providerContainer,
    child: const ScreenUtilInit(
      designSize: Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      child: App(),
    ),
  );
}
