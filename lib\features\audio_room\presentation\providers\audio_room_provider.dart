import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/analytics_utils.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/create_room_quota.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_category.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_end_reason.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_info.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_join_source.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_list_item.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_enums.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_user.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_audio_room_service.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_mini_player_service.dart';
import 'package:flutter_audio_room/features/audio_room/domain/interfaces/i_rtc_service.dart';
import 'package:flutter_audio_room/features/audio_room/domain/repositories/audio_room_repository.dart';
import 'package:flutter_audio_room/features/audio_room/domain/services/room_message_stream.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/handlers/base/room_event_handler_interface.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/handlers/message/room_message_handler.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/handlers/room_event_handler.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/handlers/storage/room_storage_handler.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/audio_room_provider_actions_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/audio_room_provider_base_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/audio_room_provider_init_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/audio_room_provider_metadata_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/audio_room_provider_mic_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/audio_room_send_gift_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_state.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/unified_message_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/services/chat_service.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/utils/callback_handlers.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/utils/default_room_message.dart';
import 'package:flutter_audio_room/features/authentication/data/model/user_info_model.dart';
import 'package:flutter_audio_room/features/instant_chat/presentation/provider/instant_call_provider.dart';
import 'package:flutter_audio_room/features/voice_call/friend/provider/friend_voice_call_provider.dart';
import 'package:flutter_audio_room/services/crypto_service/i_crypto_service.dart';
import 'package:flutter_audio_room/services/gift_service/domain/repositories/i_gift_repository.dart';
import 'package:flutter_audio_room/services/permission_service/permission_service.dart';
import 'package:flutter_audio_room/services/screen_protector_service/i_screen_protector_service.dart';
import 'package:flutter_audio_room/shared/data/consts/sp_keys.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/error_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uuid/uuid.dart';

part 'audio_room_provider.g.dart';

@Riverpod(keepAlive: true)
class AudioRoom extends _$AudioRoom
    with
        AudioRoomProviderBaseMixin,
        AudioRoomProviderMicMixin,
        AudioRoomProviderMetadataMixin,
        AudioRoomProviderInitMixin,
        AudioRoomActionsMixin,
        AudioRoomSendGiftMixin {
  late IAudioRoomService _audioService;
  late PermissionService _permissionService;
  late AudioRoomRepository _repository;
  late IGiftRepository _giftRepository;
  late RoomEventHandlerInterface _eventHandler;
  late DefaultRoomMessage _defaultRoomMessage;
  late StorageService _storageService;
  late ICryptoService _cryptoService;
  late IScreenProtectorService _screenProtectorService;
  late CallbackQueue _callbackQueue;

  final Map<String, ConnectionStateChangedCallback>
      _connectionStateChangedCallbacks = {};
  AudioVolumeCallback? _audioVolumeCallback;

  // 消息流服务
  late RoomMessageStream _messageStream;

  // 聊天服务
  ChatService? _chatService;

  // 缓存的房间消息（在聊天服务初始化前收到的消息）
  final List<RoomMessageModel> _pendingRoomMessages = [];

  @override
  StreamController<RoomMessageModel> get messageStream =>
      _messageStream.controller;

  @override
  @protected
  PermissionService get permissionService => _permissionService;
  @override
  @protected
  IAudioRoomService get audioService => _audioService;
  @override
  @protected
  IGiftRepository get giftRepository => _giftRepository;

  /// 获取消息流，供UI组件订阅
  Stream<RoomMessageModel> get messageStreamData => _messageStream.stream;

  /// 获取特定类型消息的流
  Stream<RoomMessageModel> getMessageStreamByEvent(RoomMessageEvent event) {
    return _messageStream.getByEvent(event);
  }

  /// 获取特定子类型消息的流
  Stream<RoomMessageModel> getMessageStreamBySubtype(
      RoomMessageEventSubtype subtype) {
    return _messageStream.getBySubtype(subtype);
  }

  @override
  @protected
  DefaultRoomMessage get defaultRoomMessage => _defaultRoomMessage;
  @override
  @protected
  ICryptoService get cryptoService => _cryptoService;
  @override
  @protected
  IScreenProtectorService get screenProtectorService => _screenProtectorService;
  @override
  @protected
  CallbackQueue get callbackQueue => _callbackQueue;
  @override
  @protected
  RoomEventHandlerInterface get eventHandler => _eventHandler;
  @override
  @protected
  Map<String, ConnectionStateChangedCallback>
      get connectionStateChangedCallbacks => _connectionStateChangedCallbacks;
  @override
  AudioVolumeCallback? get audioVolumeCallback => _audioVolumeCallback;
  @override
  @protected
  StorageService get storageService => _storageService;
  @override
  @protected
  AudioRoomRepository get repository => _repository;

  @override
  AudioRoomState build() {
    _messageStream = RoomMessageStream();
    final audioService = getIt<IAudioRoomService>();
    final audioRoomRepository = getIt<AudioRoomRepository>();
    final messageHandler = RoomMessageHandler(
      audioService,
      audioRoomRepository,
      _messageStream.controller,
    );
    final storageHandler = RoomStorageHandler(audioService.setClientRole);
    const uuid = Uuid();
    final defaultRoomMessage = DefaultRoomMessage(uuid);

    _audioService = audioService;
    _permissionService = getIt<PermissionService>();
    _repository = audioRoomRepository;
    _giftRepository = getIt<IGiftRepository>();
    _eventHandler = RoomEventHandler(
      audioService,
      messageHandler,
      storageHandler,
      _addMessage,
    );
    _defaultRoomMessage = defaultRoomMessage;
    _storageService = getIt<StorageService>();
    _cryptoService = getIt<ICryptoService>();
    _screenProtectorService = getIt<IScreenProtectorService>();
    _callbackQueue = CallbackQueue();

    ref.onDispose(() {
      _connectionStateChangedCallbacks.clear();
      _audioVolumeCallback = null;
      _messageStream.dispose();
      _chatService?.dispose();
      _audioService.dispose();
      ref.invalidate(unifiedMessageProvider);
    });

    return const AudioRoomState();
  }

  void _addMessage(RoomMessageModel message) {
    ref.read(unifiedMessageProvider.notifier).addRoomMessage(message);
  }

  @override
  void addMessage(RoomMessageModel message) {
    _addMessage(message);
  }

  @override
  void resetState() async {
    state = const AudioRoomState();
    _audioVolumeCallback = null;
    _connectionStateChangedCallbacks.clear();
    ref.read(unifiedMessageProvider.notifier).clear();
  }

  void setConnectionStateChangedCallback(
      String key, ConnectionStateChangedCallback callback) {
    _connectionStateChangedCallbacks[key] = callback;
  }

  void removeConnectionStateChangedCallback(String key) {
    _connectionStateChangedCallbacks.remove(key);
  }

  void setAudioVolumeCallback(AudioVolumeCallback callback) {
    _audioVolumeCallback = null;
    _audioVolumeCallback = callback;
  }

  Future<ResultWithData<CreateRoomQuota>> getCreateQuota() async {
    final result = await _repository.getCreateRoomQuota();
    return result.fold(
      (error) => Left(ErrorHandler.createValidationError(error.message)),
      (quota) => Right(quota),
    );
  }

  Future<ResultWithData<List<RoomListItem>>> getRecommendRooms({
    required bool isRefresh,
    String? category,
  }) async {
    final result = await _repository.getRecommendRooms(
      isRefresh: isRefresh,
      category: category,
    );
    return result.fold(
      (error) => Left(ErrorHandler.createValidationError(error.message)),
      (rooms) => Right(rooms),
    );
  }

  Future<ResultWithData<List<RoomListItem>>> getNewestRooms({
    required bool isRefresh,
    String? category,
  }) async {
    final result = await _repository.getNewestRooms(
      isRefresh: isRefresh,
      category: category,
    );
    return result.fold(
      (error) => Left(ErrorHandler.createValidationError(error.message)),
      (rooms) => Right(rooms),
    );
  }

  Future<ResultWithData<List<RoomListItem>>> searchRooms({
    required String keyword,
    required bool isRefresh,
  }) async {
    return await _repository.searchRooms(
      isRefresh: isRefresh,
      searchKey: keyword,
    );
  }

  Future<VoidResult> checkVoiceCall() async {
    try {
      final friendCallState = ref.read(friendVoiceCallProvider);
      final isInFriendCall = friendCallState.isCallActive;
      if (isInFriendCall) {
        return Left(
            ErrorHandler.createValidationError('You have an incoming call'));
      }

      final instantCallState = ref.read(instantCallProvider);
      final isInInstantCall = instantCallState.isCallActive;
      if (isInInstantCall) {
        return Left(
            ErrorHandler.createValidationError('You have an incoming call'));
      }
    } catch (e) {
      LogUtils.e(e.toString(), tag: 'AudioRoomProvider.checkVoiceCall');
      return Left(ErrorHandler.createValidationError(e.toString()));
    }
    return const Right(null);
  }

  Future<ResultWithData<RoomInfo?>> getRandomRoomInfo({
    String? category,
  }) async {
    final result = await _repository.getRandomRoomInfo(category: category);
    return result.fold(
      (error) => Left(ErrorHandler.createValidationError(error.message)),
      (room) => Right(room),
    );
  }

  /// Create audio room
  ///
  /// [roomName] Room name
  /// [roomPassword] Room password
  Future<VoidResult> createAndJoinAudioRoom({
    required UserInfoModel user,
    required String title,
    required RoomCategory category,
    required String announcement,
    required String background,
    required RoomType type,
    required String deduplicateKey,
    bool retry = true,
  }) async {
    if (state.currentRoom != null) {
      return Left(
        ErrorHandler.createValidationError('You are already in another room'),
      );
    }

    final checkVoiceCallResult = await checkVoiceCall();
    if (checkVoiceCallResult.isLeft()) return checkVoiceCallResult;

    final localRoomInfo = checkLocalRoomInfo();
    if (localRoomInfo != null) {
      final joinResult = await joinRoom(
        user: user,
        room: localRoomInfo,
        joinSource: RoomJoinSource.creator_reconnect,
      );
      return joinResult.fold(
        (failure) {
          if (retry && failure.identifier.contains('room.not.exist')) {
            return createAndJoinAudioRoom(
              user: user,
              title: title,
              category: category,
              announcement: announcement,
              background: background,
              type: type,
              deduplicateKey: deduplicateKey,
              retry: false,
            );
          }
          return Left(failure);
        },
        (response) => Right(response),
      );
    }

    final response = await _repository.createAudioRoom(
      title: title,
      category: category,
      announcement: announcement,
      background: background,
      type: type,
      deduplicateKey: deduplicateKey,
    );

    return response.fold(
      (failure) async {
        // Track room creation failure
        await AnalyticsUtils.trackRoomCreated(
          roomId: '',
          roomTitle: title,
          category: category,
          roomType: type,
          success: false,
          errorMessage: failure.message,
        );

        if (failure.identifier.contains('room.not.exist')) {
          await _storageService.remove(SPKeys.roomInfo);
        }
        return Left(failure);
      },
      (response) async {
        final roomInfo = response.roomInfo;
        final roomAccessInfo = response.roomAccessInfo;
        final uid = roomAccessInfo?.uid ?? -1;

        if (roomInfo == null) {
          return Left(ErrorHandler.createValidationError('Invalid room info'));
        }

        // Track successful room creation
        await AnalyticsUtils.trackRoomCreated(
          roomId: roomInfo.id ?? '',
          roomTitle: title,
          category: category,
          roomType: type,
          success: true,
        );

        await _storageService.set(
          SPKeys.roomInfo,
          jsonEncode(roomInfo.toJson()),
        );

        return await joinChannel(
          channelId: roomInfo.id ?? '',
          user: user,
          roomInfo: roomInfo,
          token: roomAccessInfo?.token ?? '',
          uid: uid,
        );
      },
    );
  }

  /// Join audio room
  ///
  /// [room] Room information
  /// [uid] User ID
  Future<VoidResult> joinRoom({
    required UserInfoModel user,
    required RoomInfo room,
    required RoomJoinSource joinSource,
  }) async {
    if (state.currentRoom?.id == room.id) {
      return const Right(null);
    } else if (state.currentRoom != null) {
      return Left(
        ErrorHandler.createValidationError('You are already in another room'),
      );
    }

    final checkVoiceCallResult = await checkVoiceCall();
    if (checkVoiceCallResult.isLeft()) return checkVoiceCallResult;

    var roomInfo = room;

    final localRoomInfo = checkLocalRoomInfo();
    if (localRoomInfo != null && localRoomInfo.id != room.id) {
      await LoadingUtils.showToast('You are already in another room');
      roomInfo = localRoomInfo;
      await Future.delayed(const Duration(seconds: 2));
      await LoadingUtils.showLoading();
    }

    final result = await _repository.joinAudioRoom(
      roomId: roomInfo.id ?? '',
      joinSource: joinSource,
    );

    return result.fold(
      (failure) async {
        if (failure.identifier.contains('room.not.exist')) {
          await _storageService.remove(SPKeys.roomInfo);
        }
        return Left(failure);
      },
      (response) async {
        return await joinChannel(
          channelId: roomInfo.id ?? '',
          user: user,
          roomInfo: roomInfo,
          token: response.token ?? '',
          uid: response.uid ?? -1,
        );
      },
    );
  }

  Future<VoidResult> joinChannel({
    required String channelId,
    required String token,
    required int uid,
    required UserInfoModel user,
    required RoomInfo roomInfo,
  }) async {
    if (!validateTokens(token)) {
      return Left(ErrorHandler.createValidationError('Invalid token'));
    }

    resetState();

    final roomUser = RoomUser(
      userId: user.profile?.id ?? '',
      uid: uid,
      firstName: user.profile?.nickName ?? '',
      avatarUrl: user.profile?.avatar ?? '',
      avatarFrame: user.frame,
      lastEnterTime: DateTime.now().millisecondsSinceEpoch,
    );

    state = state.copyWith(
      currentRoom: roomInfo,
      currentUid: uid,
      currentUser: roomUser,
      members: {
        ...state.members,
        uid: roomUser,
      },
    );

    final joinResult = await _audioService.joinChannel(
      channelId: channelId,
      token: token,
      uid: uid,
      afterLogin: fetchMembers,
    );

    // 加入频道成功后，立即设置房间消息监听器，然后初始化聊天服务
    if (joinResult.isRight()) {
      LogUtils.d('频道加入成功，设置房间消息监听器', tag: 'AudioRoomProvider.joinChannel');
      // 立即设置房间消息监听器，确保不会错过任何消息
      _setupRoomMessageListener();

      LogUtils.d('开始初始化聊天服务', tag: 'AudioRoomProvider.joinChannel');
      // 延迟一点时间确保RTM服务完全就绪
      Future.delayed(const Duration(milliseconds: 500), () {
        initializeChatService();
      });
    }

    return joinResult;
  }

  /// Leave audio room
  Future<VoidResult> leaveRoom() async {
    return ErrorHandler.handle(
      action: () async {
        final uid = state.currentUid;
        final targetUserId = state.currentUser?.userId ?? '';
        if (uid != null) {
          // 1. if is on mic, drop mic first
          if (state.currentUserOnMic) {
            await dropMic(uid);
          }

          // 2. if is manager, remove manager first
          if (state.isManager) {
            await removeManager(
              targetUid: uid,
              targetUserId: targetUserId,
            );
          }
        }

        await _audioService.leaveChannel();

        getIt<IMiniPlayerService>().removeMiniPlayer();

        state = state.copyWith(
          currentRoom: null,
        );

        return const Right(null);
      },
      identifier: 'leave_room',
    );
  }

  Future<VoidResult> endRoom() async {
    return ErrorHandler.handle(
      action: () async {
        final roomId = state.currentRoom?.id;
        if (roomId == null) {
          return Left(ErrorHandler.createValidationError('Invalid room id'));
        }

        await _storageService.remove(SPKeys.roomInfo);

        // await leaveRoom();

        final endRoomResult = await _repository.endRoom(
          roomId: roomId,
          endReason: RoomEndReason.UserManual,
        );
        if (endRoomResult.isLeft()) return endRoomResult;

        state = state.copyWith(
          currentRoom: null,
        );

        return const Right(null);
      },
      identifier: 'end_room',
    );
  }

  /// Fetch room members
  Future<VoidResult> fetchMembers() async {
    return ErrorHandler.handle(
      action: () async {
        final result = await _audioService.fetchChannelMembers();
        return result.fold(
          (error) => throw error,
          (members) {
            var newMembers = Map<int, RoomUser>.from(state.members);
            for (final member in members) {
              newMembers[member.uid!] = member;
            }
            state = state.copyWith(members: newMembers);
            return const Right(null);
          },
        );
      },
      identifier: 'fetch_members',
    );
  }

  Future<VoidResult> sendFollowMessage(RoomUser user) async {
    return ErrorHandler.handle(
      action: () async {
        final message = DefaultRoomMessage(const Uuid()).createEventMessage(
            roomId: state.currentRoom?.id ?? '',
            senderId: state.currentUid,
            sender: state.currentUser,
            targetId: user.uid,
          event: RoomMessageEvent.followBack,
        );

        // 立即将消息添加到本地房间消息流中
        _addMessage(message);

        final result = await _audioService.sendChannelMessage(message);
        return result.fold(
          (error) => throw error,
          (response) {
            return const Right(null);
          },
        );
      },
      identifier: 'send_message',
    );
  }

  // ========== 聊天功能相关方法 ==========

  /// 初始化聊天服务
  Future<VoidResult> initializeChatService() async {
    LogUtils.d('开始初始化聊天服务', tag: 'AudioRoomProvider.initializeChatService');

    if (_chatService != null) {
      LogUtils.d('聊天服务已存在，直接返回成功',
          tag: 'AudioRoomProvider.initializeChatService');
      return const Right(null);
    }

    // 检查RTM服务状态
    final currentRoom = state.currentRoom;
    final currentUid = state.currentUid;

    if (currentRoom == null || currentUid == null) {
      LogUtils.e('房间或用户信息不完整，无法初始化聊天服务',
          tag: 'AudioRoomProvider.initializeChatService');
      return Left(ErrorHandler.createValidationError('房间或用户信息不完整'));
    }

    LogUtils.d('房间信息: ${currentRoom.id}, 用户UID: $currentUid',
        tag: 'AudioRoomProvider.initializeChatService');

    try {
      LogUtils.d('调用AudioService初始化聊天服务',
          tag: 'AudioRoomProvider.initializeChatService');
      final result = await _audioService.initializeChatService();

      if (result.isRight()) {
        LogUtils.d('聊天服务初始化成功，设置消息流监听',
            tag: 'AudioRoomProvider.initializeChatService');
        // 设置消息流监听
        _setupChatMessageListener();
      } else {
        LogUtils.e(
            '聊天服务初始化失败: ${result.fold((l) => l.toString(), (r) => 'Unknown')}',
            tag: 'AudioRoomProvider.initializeChatService');
      }

      return result;
    } catch (e) {
      LogUtils.e('初始化聊天服务时发生异常: $e',
          tag: 'AudioRoomProvider.initializeChatService');
      return Left(ErrorHandler.createValidationError(e.toString()));
    }
  }

  /// 设置房间消息监听器（在加入频道后立即设置）
  void _setupRoomMessageListener() {
    LogUtils.d('设置房间消息监听器', tag: 'AudioRoomProvider._setupRoomMessageListener');

    // 监听房间消息并转换为聊天消息
    _messageStream.stream.listen((roomMessage) {
      // 如果聊天服务已初始化，直接处理；否则缓存消息
      if (isChatServiceInitialized) {
        _audioService.handleRoomMessageForChat(roomMessage);
      }
    });
  }

  /// 设置聊天消息监听器（在聊天服务初始化后设置）
  void _setupChatMessageListener() {
    // 监听聊天消息流并添加到统一Provider
    _audioService.chatMessageStream?.listen((message) {
      ref.read(unifiedMessageProvider.notifier).addChatMessage(message);
    });

    // 处理缓存的房间消息
    _processPendingRoomMessages();
  }

  /// 发送文字聊天消息
  Future<VoidResult> sendChatMessage({
    required String content,
    String? roomId,
  }) async {
    if (!isChatServiceInitialized) {
      final initResult = await initializeChatService();
      if (initResult.isLeft()) return initResult;
    }

    final currentUser = state.currentUser;
    final currentUid = state.currentUid;

    if (currentUser == null || currentUid == null) {
      return Left(ErrorHandler.createValidationError('User not found'));
    }

    return await _audioService.sendChatMessage(
      content: content,
      senderId: currentUid,
      senderName: currentUser.firstName ?? 'Unknown',
      senderAvatar: currentUser.avatarUrl,
      roomId: roomId ?? state.currentRoom?.id.toString(),
    );
  }

  /// 添加系统聊天消息
  void addSystemChatMessage(String content, {String? roomId}) {
    _audioService.addSystemMessage(
      content,
      roomId: roomId ?? state.currentRoom?.id.toString(),
    );
  }

  /// 获取聊天消息流
  Stream<ChatMessageModel>? get chatMessageStream =>
      _audioService.chatMessageStream;

  /// 获取文字消息流
  Stream<ChatMessageModel>? get textMessageStream =>
      _audioService.textMessageStream;

  /// 获取系统消息流
  Stream<ChatMessageModel>? get systemMessageStream =>
      _audioService.systemMessageStream;

  /// 检查聊天服务是否已初始化
  bool get isChatServiceInitialized => _audioService.chatMessageStream != null;

  /// 处理缓存的房间消息
  void _processPendingRoomMessages() {
    if (_pendingRoomMessages.isEmpty) {
      return;
    }

    // 处理所有缓存的消息
    for (final roomMessage in _pendingRoomMessages) {
      _audioService.handleRoomMessageForChat(roomMessage);
    }

    // 清空缓存
    _pendingRoomMessages.clear();
  }
}
