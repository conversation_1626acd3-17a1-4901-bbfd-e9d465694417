# Firebase Analytics 工具类使用指南

## 概述

`AnalyticsUtils` 是一个封装了 Firebase Analytics 功能的工具类，为音频房间应用提供全面的用户行为分析和事件追踪功能。

## 功能特性

### 1. 用户认证事件
- 用户注册 (`trackSignup`)
- 用户登录 (`trackLogin`)
- 用户登出 (`trackLogout`)
- 用户属性设置 (`setUserProperties`)

### 2. 音频房间事件
- 房间创建 (`trackRoomCreated`)
- 房间加入 (`trackRoomJoined`)
- 房间离开 (`trackRoomLeft`)
- 房间结束 (`trackRoomEnded`)

### 3. 用户交互事件
- 麦克风操作 (`trackMicAction`)
- 消息发送 (`trackMessageSent`)
- 按钮点击 (`trackButtonClick`)
- 屏幕浏览 (`trackScreenView`)

### 4. 应用生命周期事件
- 应用启动 (`trackAppLaunch`)
- 应用状态变化 (`trackAppStateChange`)

### 5. 错误和性能追踪
- 错误事件 (`trackError`)
- 性能指标 (`trackPerformance`)

### 6. 自定义事件
- 自定义事件 (`trackCustomEvent`)
- 用户参与度 (`trackUserEngagement`)

## 初始化

工具类已在 `main.dart` 中自动初始化：

```dart
// 在 _initializeFirebase() 方法中
await AnalyticsUtils.initialize();
```

## 使用示例

### 认证事件追踪

```dart
// 用户注册成功后
await AnalyticsUtils.trackSignup(
  authType: AuthType.phone,
  method: 'verification_code',
  success: true,
);

// 设置用户属性
await AnalyticsUtils.setUserProperties(
  userId: user.userId,
  username: user.username,
  country: user.countryCode,
  language: 'zh-CN',
);

// 用户登录
await AnalyticsUtils.trackLogin(
  authType: AuthType.phone,
  method: 'password',
  success: true,
);

// 用户登出
await AnalyticsUtils.trackLogout(reason: 'user_initiated');
await AnalyticsUtils.clearUserData();
```

### 音频房间事件追踪

```dart
// 房间创建
await AnalyticsUtils.trackRoomCreated(
  roomId: roomInfo.roomId,
  roomTitle: roomInfo.title,
  category: RoomCategory.music,
  roomType: RoomType.public,
  success: true,
);

// 房间加入
await AnalyticsUtils.trackRoomJoined(
  roomId: roomInfo.roomId,
  joinSource: 'room_list',
  roomTitle: roomInfo.title,
  category: roomInfo.category,
  memberCount: roomInfo.memberCount,
  success: true,
);

// 麦克风操作
await AnalyticsUtils.trackMicAction(
  roomId: currentRoom.roomId,
  action: 'take_mic',
  seatPosition: 2,
  success: true,
);

// 消息发送
await AnalyticsUtils.trackMessageSent(
  roomId: currentRoom.roomId,
  messageType: 'text',
  messageLength: message.length,
);
```

### 屏幕和交互追踪

```dart
// 屏幕浏览
await AnalyticsUtils.trackScreenView(
  screenName: 'audio_room_screen',
  screenClass: 'AudioRoomScreen',
  parameters: {
    'room_id': roomId,
    'room_type': 'public',
  },
);

// 按钮点击
await AnalyticsUtils.trackButtonClick(
  buttonName: 'create_room',
  screenName: 'home_screen',
  additionalParams: {
    'user_quota': userQuota,
  },
);
```

### 错误和性能追踪

```dart
// 错误追踪
await AnalyticsUtils.trackError(
  errorType: 'api_error',
  errorMessage: 'Failed to join room',
  errorCode: '404',
  context: 'AudioRoomRepository.joinAudioRoom',
);

// 性能追踪
final stopwatch = Stopwatch()..start();
// 执行操作...
stopwatch.stop();

await AnalyticsUtils.trackPerformance(
  operationName: 'room_join',
  durationMs: stopwatch.elapsedMilliseconds,
  success: true,
  additionalInfo: 'room_id: $roomId',
);
```

## 集成建议

### 1. 认证模块集成
在 `AuthNotifier` 类中的相应方法中添加分析事件：
- `signup()` 方法中添加 `trackSignup()`
- `login()` 方法中添加 `trackLogin()`
- `logout()` 方法中添加 `trackLogout()`

### 2. 音频房间模块集成
在 `AudioRoomProvider` 类中的相应方法中添加分析事件：
- `createAndJoinAudioRoom()` 方法中添加 `trackRoomCreated()`
- `joinRoom()` 方法中添加 `trackRoomJoined()`
- `leaveRoom()` 方法中添加 `trackRoomLeft()`
- 麦克风相关方法中添加 `trackMicAction()`

### 3. UI 组件集成
在各个屏幕和组件中添加：
- 屏幕的 `initState()` 或 `build()` 方法中添加 `trackScreenView()`
- 按钮的 `onPressed` 回调中添加 `trackButtonClick()`
- 消息发送功能中添加 `trackMessageSent()`

### 4. 错误处理集成
在错误处理代码中添加：
- API 错误处理中添加 `trackError()`
- 网络错误处理中添加 `trackError()`
- 验证错误处理中添加 `trackError()`

## 最佳实践

### 1. 事件命名规范
- 使用小写字母和下划线
- 保持事件名称简洁明了
- 使用一致的命名约定

### 2. 参数设置
- 只包含必要的参数
- 避免包含敏感信息
- 使用标准化的参数值

### 3. 性能考虑
- 分析调用是异步的，不会阻塞 UI
- 在 debug 模式下自动禁用数据收集
- 避免在循环中频繁调用分析方法

### 4. 隐私保护
- 不要记录个人身份信息
- 遵循数据保护法规
- 提供用户选择退出的选项

## 调试和测试

### 1. 调试模式
在调试模式下，分析数据收集被自动禁用，但会在控制台输出日志信息。

### 2. 手动控制
```dart
// 手动启用/禁用分析
await AnalyticsUtils.setAnalyticsEnabled(false);

// 重置分析数据
await AnalyticsUtils.resetAnalyticsData();
```

### 3. 查看日志
所有分析事件都会通过 `LogUtils` 输出调试信息，便于开发时查看。

## 注意事项

1. **初始化顺序**：确保在 Firebase 初始化后再调用 Analytics 方法
2. **错误处理**：所有 Analytics 方法都包含错误处理，不会影响应用正常运行
3. **数据类型**：参数必须是 Firebase Analytics 支持的类型（String, int, double, bool）
4. **事件限制**：Firebase Analytics 对事件名称和参数有一定限制，请参考官方文档

## 相关文件

- `lib/core/utils/analytics_utils.dart` - 主要工具类
- `lib/core/utils/analytics_integration_examples.dart` - 集成示例
- `lib/main.dart` - 初始化代码

## 更多信息

- [Firebase Analytics 官方文档](https://firebase.google.com/docs/analytics)
- [Flutter Firebase Analytics 插件](https://pub.dev/packages/firebase_analytics)
