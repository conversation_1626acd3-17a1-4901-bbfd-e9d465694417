// This file contains examples of how to integrate AnalyticsUtils into your app features
// Copy these examples to the appropriate files in your project

import 'package:flutter_audio_room/core/utils/analytics_utils.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_enums.dart';
import 'package:flutter_audio_room/features/authentication/data/model/auth_enums.dart';

/// Examples for Authentication Features
class AuthenticationAnalyticsExamples {
  
  /// Example: Track user signup in AuthNotifier.signup method
  static Future<void> exampleSignupTracking() async {
    // Add this in AuthNotifier.signup method after successful signup
    await AnalyticsUtils.trackSignup(
      authType: AuthType.phone, // or AuthType.email
      method: 'verification_code',
      success: true,
    );
    
    // Set user properties after successful signup
    await AnalyticsUtils.setUserProperties(
      userId: 'user_id_here',
      username: 'username_here',
      country: 'country_code',
      language: 'language_code',
    );
  }
  
  /// Example: Track user login in AuthNotifier.login method
  static Future<void> exampleLoginTracking() async {
    // Add this in AuthNotifier.login method after successful login
    await AnalyticsUtils.trackLogin(
      authType: AuthType.phone,
      method: 'password', // or 'verification_code'
      success: true,
    );
  }
  
  /// Example: Track login failure
  static Future<void> exampleLoginFailureTracking() async {
    // Add this in AuthNotifier.login method when login fails
    await AnalyticsUtils.trackLogin(
      authType: AuthType.phone,
      method: 'password',
      success: false,
      errorMessage: 'Invalid credentials',
    );
  }
  
  /// Example: Track logout in AuthNotifier.logout method
  static Future<void> exampleLogoutTracking() async {
    // Add this in AuthNotifier.logout method
    await AnalyticsUtils.trackLogout(reason: 'user_initiated');
    
    // Clear user data
    await AnalyticsUtils.clearUserData();
  }
}

/// Examples for Audio Room Features
class AudioRoomAnalyticsExamples {
  
  /// Example: Track room creation in AudioRoomProvider.createAndJoinAudioRoom
  static Future<void> exampleRoomCreationTracking() async {
    // Add this after successful room creation
    await AnalyticsUtils.trackRoomCreated(
      roomId: 'room_id_here',
      roomTitle: 'My Room',
      category: RoomCategory.music, // adjust based on actual category
      roomType: RoomType.public, // adjust based on actual type
      success: true,
    );
  }
  
  /// Example: Track room creation failure
  static Future<void> exampleRoomCreationFailureTracking() async {
    // Add this when room creation fails
    await AnalyticsUtils.trackRoomCreated(
      roomId: '',
      roomTitle: 'My Room',
      category: RoomCategory.music,
      roomType: RoomType.public,
      success: false,
      errorMessage: 'Failed to create room: quota exceeded',
    );
  }
  
  /// Example: Track room join in AudioRoomProvider.joinRoom
  static Future<void> exampleRoomJoinTracking() async {
    // Add this after successful room join
    await AnalyticsUtils.trackRoomJoined(
      roomId: 'room_id_here',
      joinSource: 'room_list', // or 'random_join', 'invitation', etc.
      roomTitle: 'Room Title',
      category: RoomCategory.music,
      memberCount: 5,
      success: true,
    );
  }
  
  /// Example: Track room leave in AudioRoomProvider.leaveRoom
  static Future<void> exampleRoomLeaveTracking() async {
    // Add this when user leaves room
    await AnalyticsUtils.trackRoomLeft(
      roomId: 'room_id_here',
      reason: 'user_initiated',
      durationSeconds: 1800, // 30 minutes
      wasCreator: false,
    );
  }
  
  /// Example: Track room end in AudioRoomProvider.endRoom
  static Future<void> exampleRoomEndTracking() async {
    // Add this when room creator ends the room
    await AnalyticsUtils.trackRoomEnded(
      roomId: 'room_id_here',
      durationSeconds: 3600, // 1 hour
      maxMemberCount: 12,
      reason: 'creator_ended',
    );
  }
  
  /// Example: Track mic actions
  static Future<void> exampleMicActionTracking() async {
    // Add this when user takes mic
    await AnalyticsUtils.trackMicAction(
      roomId: 'room_id_here',
      action: 'take_mic',
      seatPosition: 2,
      success: true,
    );
    
    // Add this when user drops mic
    await AnalyticsUtils.trackMicAction(
      roomId: 'room_id_here',
      action: 'drop_mic',
      seatPosition: 2,
      success: true,
    );
    
    // Add this when user mutes/unmutes
    await AnalyticsUtils.trackMicAction(
      roomId: 'room_id_here',
      action: 'mute',
      success: true,
    );
  }
  
  /// Example: Track message sent
  static Future<void> exampleMessageTracking() async {
    // Add this when user sends a message
    await AnalyticsUtils.trackMessageSent(
      roomId: 'room_id_here',
      messageType: 'text',
      messageLength: 25,
    );
    
    // Add this when user sends emoji
    await AnalyticsUtils.trackMessageSent(
      roomId: 'room_id_here',
      messageType: 'emoji',
    );
    
    // Add this when user sends gift
    await AnalyticsUtils.trackMessageSent(
      roomId: 'room_id_here',
      messageType: 'gift',
    );
  }
}

/// Examples for Screen Navigation
class ScreenAnalyticsExamples {
  
  /// Example: Track screen views in your route observer or page widgets
  static Future<void> exampleScreenViewTracking() async {
    // Add this in your screen widgets' initState or build method
    await AnalyticsUtils.trackScreenView(
      screenName: 'home_screen',
      screenClass: 'HomeScreen',
      parameters: {
        'tab': 'rooms',
        'user_level': 'premium',
      },
    );
    
    await AnalyticsUtils.trackScreenView(
      screenName: 'audio_room_screen',
      screenClass: 'AudioRoomScreen',
      parameters: {
        'room_id': 'room_123',
        'room_type': 'public',
      },
    );
    
    await AnalyticsUtils.trackScreenView(
      screenName: 'profile_screen',
      screenClass: 'ProfileScreen',
    );
  }
  
  /// Example: Track button clicks
  static Future<void> exampleButtonClickTracking() async {
    // Add this in your button onPressed callbacks
    await AnalyticsUtils.trackButtonClick(
      buttonName: 'create_room',
      screenName: 'home_screen',
      additionalParams: {
        'user_quota': 3,
      },
    );
    
    await AnalyticsUtils.trackButtonClick(
      buttonName: 'join_random_room',
      screenName: 'home_screen',
    );
    
    await AnalyticsUtils.trackButtonClick(
      buttonName: 'send_message',
      screenName: 'audio_room_screen',
      additionalParams: {
        'room_id': 'room_123',
        'message_type': 'text',
      },
    );
  }
}

/// Examples for Error Tracking
class ErrorAnalyticsExamples {
  
  /// Example: Track API errors
  static Future<void> exampleApiErrorTracking() async {
    // Add this in your error handling code
    await AnalyticsUtils.trackError(
      errorType: 'api_error',
      errorMessage: 'Failed to join room',
      errorCode: '404',
      context: 'AudioRoomRepository.joinAudioRoom',
    );
  }
  
  /// Example: Track network errors
  static Future<void> exampleNetworkErrorTracking() async {
    await AnalyticsUtils.trackError(
      errorType: 'network_error',
      errorMessage: 'Connection timeout',
      context: 'NetworkService.post',
    );
  }
  
  /// Example: Track validation errors
  static Future<void> exampleValidationErrorTracking() async {
    await AnalyticsUtils.trackError(
      errorType: 'validation_error',
      errorMessage: 'Invalid room name',
      context: 'CreateRoomForm.validate',
    );
  }
}

/// Examples for Performance Tracking
class PerformanceAnalyticsExamples {
  
  /// Example: Track room join performance
  static Future<void> exampleRoomJoinPerformanceTracking() async {
    final stopwatch = Stopwatch()..start();
    
    // Your room join logic here
    // ...
    
    stopwatch.stop();
    
    await AnalyticsUtils.trackPerformance(
      operationName: 'room_join',
      durationMs: stopwatch.elapsedMilliseconds,
      success: true,
      additionalInfo: 'room_id: room_123',
    );
  }
  
  /// Example: Track app startup performance
  static Future<void> exampleAppStartupPerformanceTracking() async {
    final stopwatch = Stopwatch()..start();
    
    // Your app initialization logic here
    // ...
    
    stopwatch.stop();
    
    await AnalyticsUtils.trackPerformance(
      operationName: 'app_startup',
      durationMs: stopwatch.elapsedMilliseconds,
      success: true,
    );
  }
}

/// Examples for Custom Events
class CustomAnalyticsExamples {
  
  /// Example: Track feature usage
  static Future<void> exampleFeatureUsageTracking() async {
    await AnalyticsUtils.trackUserEngagement(
      engagementType: 'feature_used',
      featureName: 'voice_effects',
      engagementValue: 1,
    );
    
    await AnalyticsUtils.trackUserEngagement(
      engagementType: 'daily_active',
    );
  }
  
  /// Example: Track custom business events
  static Future<void> exampleCustomBusinessEventTracking() async {
    await AnalyticsUtils.trackCustomEvent(
      eventName: 'gift_sent',
      parameters: {
        'gift_type': 'rose',
        'gift_value': 10,
        'recipient_id': 'user_456',
        'room_id': 'room_123',
      },
    );
    
    await AnalyticsUtils.trackCustomEvent(
      eventName: 'subscription_purchased',
      parameters: {
        'plan_type': 'premium',
        'duration': 'monthly',
        'price': 9.99,
      },
    );
  }
}
